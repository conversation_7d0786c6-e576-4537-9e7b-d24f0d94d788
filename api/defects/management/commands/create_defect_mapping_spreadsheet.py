from pathlib import Path

from django.core.management.base import BaseCommand

from api.defects.models import DefectModelList, DefectScores, StandardSubcategory, Standard


MANAGEMENT_FOLDER = Path(__file__).parent
DATA_FOLDER = MANAGEMENT_FOLDER.parent / "baseline_data"


def get_dml_stf_desc(dml):
    return dml["fields"]["name"].split(" - ", 1)[0]


def get_relevant_defect_model_list(defect_scores):
    dml_ids = defect_scores.values_list("defect_key_id", flat=True).distinct()
    return DefectModelList.objects.filter(id__in=dml_ids).order_by("id")


def get_relevant_defect_scores(sub_standard):
    return DefectScores.objects.filter(sub_standard=sub_standard).filter(defect_code__isnull=False).order_by("id")


def get_standard_sub_category(standard):
    standard_sub_categories = StandardSubcategory.objects.filter(standard_key=standard).order_by("id")
    print()
    for sub_standard in standard_sub_categories:
        print(f"{sub_standard.id} - {sub_standard.comment}")
    return standard_sub_categories.get(id=int(input("\nEnter the substandard id to create a defect mapping spreadsheet for: ")))


def get_standard():
    all_standards = Standard.objects.all().order_by("id")
    print()
    for standard in all_standards:
        print(f"{standard.id} - {standard.display_name}")
    return all_standards.get(id=int(input("\nEnter the standard id to select a subcategory within: ")))


class Command(BaseCommand):
    help = "Create a spreadsheet for mapping defects to the new schema for a single substandard."

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        """Handle the command execution."""
        standard = get_standard()
        sub_standard = get_standard_sub_category(standard)
        defect_scores = get_relevant_defect_scores(sub_standard)
        
